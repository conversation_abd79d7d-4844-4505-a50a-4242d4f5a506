"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_supabase_tokenUsageService_ts";
exports.ids = ["_rsc_src_lib_supabase_tokenUsageService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: true,\n            // Persistir sesión en el navegador\n            autoRefreshToken: true,\n            // Refrescar token automáticamente\n            detectSessionInUrl: true // ESENCIAL: Detectar y procesar tokens de URL\n        }\n    });\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFFbkQ7QUFDTyxTQUFTQyxZQUFZQSxDQUFBLEVBQUc7SUFDN0IsT0FBT0Qsa0VBQW1CLENBQ3hCRSwwQ0FBb0MsRUFDcENBLGtOQUF5QyxFQUN6QztRQUNFSSxJQUFJLEVBQUU7WUFDSkMsY0FBYyxFQUFFLElBQUk7WUFBUTtZQUM1QkMsZ0JBQWdCLEVBQUUsSUFBSTtZQUFNO1lBQzVCQyxrQkFBa0IsRUFBRSxJQUFJLENBQUk7UUFDOUI7SUFDRixDQUNGLENBQUM7QUFDSDtBQUVBO0FBQ08sTUFBTUMsUUFBUSxHQUFHVCxZQUFZLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxzcmNcXGxpYlxcc3VwYWJhc2VcXGNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCcm93c2VyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XG5cbi8vIENsaWVudGUgcGFyYSBlbCBuYXZlZ2Fkb3IgKGNvbXBvbmVudGVzIGRlbCBjbGllbnRlKVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUJyb3dzZXJDbGllbnQoXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXG4gICAge1xuICAgICAgYXV0aDoge1xuICAgICAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSwgICAgICAgLy8gUGVyc2lzdGlyIHNlc2nDs24gZW4gZWwgbmF2ZWdhZG9yXG4gICAgICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsICAgICAvLyBSZWZyZXNjYXIgdG9rZW4gYXV0b23DoXRpY2FtZW50ZVxuICAgICAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUgICAgLy8gRVNFTkNJQUw6IERldGVjdGFyIHkgcHJvY2VzYXIgdG9rZW5zIGRlIFVSTFxuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cblxuLy8gTWFudGVuZXIgY29tcGF0aWJpbGlkYWQgY29uIGPDs2RpZ28gZXhpc3RlbnRlXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoKTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiY3JlYXRlQ2xpZW50IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiYXV0aCIsInBlcnNpc3RTZXNzaW9uIiwiYXV0b1JlZnJlc2hUb2tlbiIsImRldGVjdFNlc3Npb25JblVybCIsInN1cGFiYXNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/tokenUsageService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/tokenUsageService.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canPerformActivity: () => (/* binding */ canPerformActivity),\n/* harmony export */   checkTokenLimit: () => (/* binding */ checkTokenLimit),\n/* harmony export */   getTokenPurchaseHistory: () => (/* binding */ getTokenPurchaseHistory),\n/* harmony export */   getTokenUsageProgress: () => (/* binding */ getTokenUsageProgress),\n/* harmony export */   getUserPlanInfo: () => (/* binding */ getUserPlanInfo),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserTokenStats: () => (/* binding */ getUserTokenStats),\n/* harmony export */   saveTokenUsage: () => (/* binding */ saveTokenUsage)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\n\n\n/**\n * Guarda el uso de tokens en Supabase con validación de plan\n */ async function saveTokenUsage(data) {\n    try {\n        console.log('🔄 saveTokenUsage (cliente) iniciado con data:', data);\n        // Este servicio solo funciona en el cliente\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        console.log('✅ Cliente Supabase creado');\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');\n        if (userError || !user) {\n            console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);\n            return;\n        }\n        // Validar que el usuario tiene acceso a la actividad\n        const accessValidation = await validateActivityAccess(user.id, data.activity, data.usage.totalTokens);\n        if (!accessValidation.allowed) {\n            console.warn('❌ Acceso denegado para actividad:', accessValidation.reason);\n            throw new Error(accessValidation.reason);\n        }\n        const usageRecord = {\n            user_id: user.id,\n            activity_type: data.activity,\n            model_name: data.model,\n            prompt_tokens: data.usage.promptTokens,\n            completion_tokens: data.usage.completionTokens,\n            total_tokens: data.usage.totalTokens,\n            estimated_cost: data.usage.estimatedCost || 0,\n            usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01\n        };\n        console.log('📝 Registro a insertar:', usageRecord);\n        const { error } = await supabase.from('user_token_usage').insert([\n            usageRecord\n        ]);\n        if (error) {\n            console.error('❌ Error al guardar uso de tokens:', error);\n            return;\n        }\n        console.log('✅ Registro insertado exitosamente en user_token_usage');\n        // Actualizar contador mensual del usuario\n        await updateMonthlyTokenCount(user.id, data.usage.totalTokens);\n    } catch (error) {\n        console.error('Error en saveTokenUsage:', error);\n    }\n}\n/**\n * Actualiza el contador mensual de tokens del usuario\n */ async function updateMonthlyTokenCount(userId, tokens) {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Obtener o crear perfil del usuario\n        let { data: profile, error: profileError } = await supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (profileError && profileError.code !== 'PGRST116') {\n            console.error('Error al obtener perfil:', profileError);\n            return;\n        }\n        if (!profile) {\n            // Crear perfil nuevo\n            const { error: insertError } = await supabase.from('user_profiles').insert([\n                {\n                    user_id: userId,\n                    subscription_plan: 'free',\n                    monthly_token_limit: 50000,\n                    current_month_tokens: tokens,\n                    current_month: currentMonth\n                }\n            ]);\n            if (insertError) {\n                console.error('Error al crear perfil:', insertError);\n            }\n        } else {\n            // Actualizar perfil existente\n            const newTokenCount = profile.current_month === currentMonth ? profile.current_month_tokens + tokens : tokens; // Reset si es nuevo mes\n            const { error: updateError } = await supabase.from('user_profiles').update({\n                current_month_tokens: newTokenCount,\n                current_month: currentMonth,\n                updated_at: new Date().toISOString()\n            }).eq('user_id', userId);\n            if (updateError) {\n                console.error('Error al actualizar perfil:', updateError);\n            }\n        }\n    } catch (error) {\n        console.error('Error en updateMonthlyTokenCount:', error);\n    }\n}\n/**\n * Obtiene estadísticas de uso de tokens del usuario actual\n */ async function getUserTokenStats() {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return getEmptyStats();\n        }\n        const { data: records, error } = await supabase.from('user_token_usage').select('*').eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener estadísticas:', error);\n            return getEmptyStats();\n        }\n        return calculateStats(records || []);\n    } catch (error) {\n        console.error('Error en getUserTokenStats:', error);\n        return getEmptyStats();\n    }\n}\n/**\n * Calcula estadísticas a partir de los registros\n */ function calculateStats(records) {\n    const stats = {\n        totalSessions: records.length,\n        totalTokens: 0,\n        totalCost: 0,\n        byActivity: {},\n        byModel: {}\n    };\n    records.forEach((record)=>{\n        const tokens = record.total_tokens;\n        const cost = record.estimated_cost;\n        stats.totalTokens += tokens;\n        stats.totalCost += cost;\n        // Por actividad\n        if (!stats.byActivity[record.activity_type]) {\n            stats.byActivity[record.activity_type] = {\n                tokens: 0,\n                cost: 0,\n                count: 0\n            };\n        }\n        stats.byActivity[record.activity_type].tokens += tokens;\n        stats.byActivity[record.activity_type].cost += cost;\n        stats.byActivity[record.activity_type].count += 1;\n        // Por modelo\n        if (!stats.byModel[record.model_name]) {\n            stats.byModel[record.model_name] = {\n                tokens: 0,\n                cost: 0,\n                count: 0\n            };\n        }\n        stats.byModel[record.model_name].tokens += tokens;\n        stats.byModel[record.model_name].cost += cost;\n        stats.byModel[record.model_name].count += 1;\n    });\n    return stats;\n}\n/**\n * Retorna estadísticas vacías\n */ function getEmptyStats() {\n    return {\n        totalSessions: 0,\n        totalTokens: 0,\n        totalCost: 0,\n        byActivity: {},\n        byModel: {}\n    };\n}\n/**\n * Obtiene el perfil del usuario actual\n */ async function getUserProfile() {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return null;\n        }\n        const { data: profile, error } = await supabase.from('user_profiles').select('*').eq('user_id', user.id).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error al obtener perfil:', error);\n            return null;\n        }\n        return profile;\n    } catch (error) {\n        console.error('Error en getUserProfile:', error);\n        return null;\n    }\n}\n/**\n * Verifica si el usuario ha alcanzado su límite mensual\n */ async function checkTokenLimit() {\n    try {\n        const profile = await getUserProfile();\n        if (!profile) {\n            return {\n                hasReachedLimit: false,\n                currentTokens: 0,\n                limit: 50000,\n                percentage: 0\n            };\n        }\n        const percentage = profile.current_month_tokens / profile.monthly_token_limit * 100;\n        const hasReachedLimit = profile.current_month_tokens >= profile.monthly_token_limit;\n        return {\n            hasReachedLimit,\n            currentTokens: profile.current_month_tokens,\n            limit: profile.monthly_token_limit,\n            percentage\n        };\n    } catch (error) {\n        console.error('Error en checkTokenLimit:', error);\n        return {\n            hasReachedLimit: false,\n            currentTokens: 0,\n            limit: 50000,\n            percentage: 0\n        };\n    }\n}\n/**\n * Valida si un usuario tiene acceso a una actividad específica\n */ async function validateActivityAccess(userId, activity, tokensToUse) {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        // Obtener perfil del usuario\n        const { data: profile, error } = await supabase.from('user_profiles').select('subscription_plan, payment_verified, current_month_tokens, monthly_token_limit, current_month').eq('user_id', userId).single();\n        if (error || !profile) {\n            return {\n                allowed: false,\n                reason: 'Perfil de usuario no encontrado'\n            };\n        }\n        // Mapear actividades a características\n        const activityToFeature = {\n            'test_generation': 'test_generation',\n            'flashcard_generation': 'flashcard_generation',\n            'mind_map_generation': 'mind_map_generation',\n            'ai_chat': 'ai_tutor_chat',\n            'study_planning': 'study_planning',\n            'summary_generation': 'summary_a1_a2',\n            'document_analysis': 'document_upload'\n        };\n        const featureName = activityToFeature[activity] || activity;\n        // Verificar acceso a la característica según el plan\n        if (!(0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.hasFeatureAccess)(profile.subscription_plan, featureName)) {\n            return {\n                allowed: false,\n                reason: `La actividad ${activity} no está disponible en el plan ${profile.subscription_plan}`\n            };\n        }\n        // Verificar pago para planes de pago\n        if (profile.subscription_plan !== 'free' && !profile.payment_verified) {\n            return {\n                allowed: false,\n                reason: 'Pago no verificado. Complete el proceso de pago para usar esta función.'\n            };\n        }\n        // Verificar límites de tokens\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        let currentTokens = profile.current_month_tokens;\n        // Reset si es nuevo mes\n        if (profile.current_month !== currentMonth) {\n            currentTokens = 0;\n        }\n        if (currentTokens + tokensToUse > profile.monthly_token_limit) {\n            return {\n                allowed: false,\n                reason: `Límite mensual de tokens alcanzado. Usado: ${currentTokens}/${profile.monthly_token_limit}`\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error validating activity access:', error);\n        return {\n            allowed: false,\n            reason: 'Error interno de validación'\n        };\n    }\n}\n/**\n * Obtiene información detallada del plan del usuario\n */ async function getUserPlanInfo() {\n    try {\n        const profile = await getUserProfile();\n        if (!profile) {\n            return null;\n        }\n        const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(profile.subscription_plan);\n        if (!planConfig) {\n            return null;\n        }\n        const percentage = profile.current_month_tokens / profile.monthly_token_limit * 100;\n        return {\n            plan: profile.subscription_plan,\n            planName: planConfig.name,\n            features: planConfig.features,\n            tokenUsage: {\n                current: profile.current_month_tokens,\n                limit: profile.monthly_token_limit,\n                percentage: Math.round(percentage),\n                remaining: profile.monthly_token_limit - profile.current_month_tokens\n            },\n            paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'\n        };\n    } catch (error) {\n        console.error('Error getting user plan info:', error);\n        return null;\n    }\n}\n/**\n * Verifica si el usuario puede realizar una actividad específica antes de ejecutarla\n */ async function canPerformActivity(activity, estimatedTokens = 0) {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return {\n                allowed: false,\n                reason: 'Usuario no autenticado'\n            };\n        }\n        const validation = await validateActivityAccess(user.id, activity, estimatedTokens);\n        if (!validation.allowed) {\n            const planInfo = await getUserPlanInfo();\n            return {\n                allowed: false,\n                reason: validation.reason,\n                planInfo\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking activity permission:', error);\n        return {\n            allowed: false,\n            reason: 'Error interno de validación'\n        };\n    }\n}\n/**\n * Obtiene datos de progreso de tokens para estadísticas avanzadas\n */ async function getTokenUsageProgress() {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return null;\n        }\n        // Obtener perfil del usuario\n        const profile = await getUserProfile();\n        if (!profile) {\n            return null;\n        }\n        // Calcular porcentaje de uso\n        const percentage = profile.current_month_tokens / profile.monthly_token_limit * 100;\n        // Obtener historial diario de los últimos 30 días\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        const { data: dailyUsage, error: historyError } = await supabase.from('user_token_usage').select('created_at, total_tokens').eq('user_id', user.id).gte('created_at', thirtyDaysAgo.toISOString()).order('created_at', {\n            ascending: true\n        });\n        if (historyError) {\n            console.error('Error al obtener historial diario:', historyError);\n        }\n        // Agrupar por día\n        const dailyHistory = [];\n        const dailyMap = new Map();\n        if (dailyUsage) {\n            dailyUsage.forEach((record)=>{\n                const date = new Date(record.created_at).toISOString().split('T')[0];\n                const currentTokens = dailyMap.get(date) || 0;\n                dailyMap.set(date, currentTokens + record.total_tokens);\n            });\n            // Convertir a array ordenado\n            for(let i = 29; i >= 0; i--){\n                const date = new Date();\n                date.setDate(date.getDate() - i);\n                const dateStr = date.toISOString().split('T')[0];\n                dailyHistory.push({\n                    date: dateStr,\n                    tokens: dailyMap.get(dateStr) || 0\n                });\n            }\n        }\n        return {\n            percentage: Math.round(percentage),\n            limit: profile.monthly_token_limit,\n            used: profile.current_month_tokens,\n            remaining: profile.monthly_token_limit - profile.current_month_tokens,\n            dailyHistory\n        };\n    } catch (error) {\n        console.error('Error en getTokenUsageProgress:', error);\n        return null;\n    }\n}\n/**\n * Obtiene historial de compras de tokens del usuario\n */ async function getTokenPurchaseHistory() {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return null;\n        }\n        const { data: purchases, error } = await supabase.from('token_purchases').select('id, amount, price, created_at, status').eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener historial de compras:', error);\n            return null;\n        }\n        return purchases || [];\n    } catch (error) {\n        console.error('Error en getTokenPurchaseHistory:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/tokenUsageService.ts\n");

/***/ })

};
;