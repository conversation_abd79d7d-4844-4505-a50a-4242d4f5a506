/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/create-checkout-session/route";
exports.ids = ["app/api/stripe/create-checkout-session/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/create-checkout-session/route.ts */ \"(rsc)/./src/app/api/stripe/create-checkout-session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/create-checkout-session/route\",\n        pathname: \"/api/stripe/create-checkout-session\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/create-checkout-session/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\stripe\\\\create-checkout-session\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzdHJpcGUlMkZjcmVhdGUtY2hlY2tvdXQtc2Vzc2lvbiUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGc3RyaXBlJTJGY3JlYXRlLWNoZWNrb3V0LXNlc3Npb24lMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZzdHJpcGUlMkZjcmVhdGUtY2hlY2tvdXQtc2Vzc2lvbiUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNuYWF0YSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNPcG9zSSU1Q3YxNiU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDbmFhdGElNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDT3Bvc0klNUN2MTYmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ21FO0FBQ2hKO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFx2MTZcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc3RyaXBlXFxcXGNyZWF0ZS1jaGVja291dC1zZXNzaW9uXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9zdHJpcGUvY3JlYXRlLWNoZWNrb3V0LXNlc3Npb24vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9zdHJpcGUvY3JlYXRlLWNoZWNrb3V0LXNlc3Npb25cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N0cmlwZS9jcmVhdGUtY2hlY2tvdXQtc2Vzc2lvbi9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxzdHJpcGVcXFxcY3JlYXRlLWNoZWNrb3V0LXNlc3Npb25cXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/create-checkout-session/route.ts":
/*!*************************************************************!*\
  !*** ./src/app/api/stripe/create-checkout-session/route.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stripe/config */ \"(rsc)/./src/lib/stripe/config.ts\");\n// src/app/api/stripe/create-checkout-session/route.ts\n\n\nasync function POST(request) {\n    try {\n        console.log('Stripe API called');\n        const body = await request.json();\n        console.log('Request body:', body);\n        const { planId, email, customerName, userId, registrationData } = body;\n        // Validaciones básicas\n        if (!planId || !email) {\n            console.log('Missing planId or email');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Plan ID y email son requeridos'\n            }, {\n                status: 400\n            });\n        }\n        console.log('Validating plan:', planId);\n        if (!(0,_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.isValidPlan)(planId)) {\n            console.log('Invalid plan ID:', planId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Plan ID no válido'\n            }, {\n                status: 400\n            });\n        }\n        const plan = (0,_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.getPlanById)(planId);\n        console.log('Plan found:', plan);\n        if (!plan) {\n            console.log('Plan not found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Plan no encontrado'\n            }, {\n                status: 404\n            });\n        }\n        // El plan gratuito no requiere pago\n        if (planId === 'free') {\n            console.log('Free plan does not require payment');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'El plan gratuito no requiere pago'\n            }, {\n                status: 400\n            });\n        }\n        console.log('Creating checkout session for:', {\n            planId,\n            email,\n            customerName\n        });\n        // Usar el precio fijo configurado\n        const priceId = plan.stripePriceId;\n        if (!priceId) {\n            console.log('No price ID configured for plan:', planId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Precio no configurado para este plan'\n            }, {\n                status: 500\n            });\n        }\n        console.log('Using price ID:', priceId);\n        // Determinar el modo de pago basado en el plan\n        // Ahora 'usuario' y 'pro' son recurrentes (suscripciones mensuales)\n        const isRecurring = planId === 'pro' || planId === 'usuario';\n        const mode = isRecurring ? 'subscription' : 'payment';\n        console.log('Payment mode:', mode, 'for plan:', planId);\n        // Crear sesión de checkout\n        const sessionConfig = {\n            payment_method_types: [\n                'card'\n            ],\n            line_items: [\n                {\n                    price: priceId,\n                    quantity: 1\n                }\n            ],\n            mode: mode,\n            customer_email: email,\n            client_reference_id: planId,\n            metadata: {\n                planId: planId,\n                customerEmail: email,\n                customerName: customerName || '',\n                userId: userId || '',\n                // ID del usuario pre-registrado (vacío para nuevo flujo)\n                registrationData: registrationData ? JSON.stringify(registrationData) : '',\n                // Datos de registro para crear cuenta después del pago\n                createdAt: new Date().toISOString(),\n                source: 'oposiai_website',\n                autoActivate: 'true'\n            },\n            success_url: `${_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.APP_URLS.success}?session_id={CHECKOUT_SESSION_ID}&plan=${planId}`,\n            cancel_url: `${_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.APP_URLS.cancel}?plan=${planId}`,\n            automatic_tax: {\n                enabled: true\n            },\n            billing_address_collection: 'required',\n            allow_promotion_codes: true\n        };\n        // Agregar configuración específica para suscripciones (AHORA APLICA A 'usuario' Y 'pro')\n        if (isRecurring) {\n            sessionConfig.subscription_data = {\n                metadata: {\n                    planId: planId,\n                    customerEmail: email,\n                    customerName: customerName || '',\n                    userId: userId || '',\n                    // ID del usuario pre-registrado (vacío para nuevo flujo)\n                    registrationData: registrationData ? JSON.stringify(registrationData) : '',\n                    // Datos de registro para crear cuenta después del pago\n                    source: 'oposiai_website',\n                    autoActivate: 'true'\n                }\n            };\n        }\n        if (!_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.stripe) {\n            console.error('Stripe not initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error de configuración de Stripe'\n            }, {\n                status: 500\n            });\n        }\n        const session = await _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.stripe.checkout.sessions.create(sessionConfig);\n        console.log('Checkout session created:', session.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            sessionId: session.id,\n            url: session.url\n        });\n    } catch (error) {\n        console.error('Error creating checkout session:', error);\n        console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');\n        let errorMessage = 'Error al crear la sesión de pago';\n        if (error instanceof Error) {\n            errorMessage = error.message;\n            console.error('Error message:', errorMessage);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage,\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9zdHJpcGUvY3JlYXRlLWNoZWNrb3V0LXNlc3Npb24vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDMEM7QUFDc0M7QUFFekUsZUFBZUssSUFBSUEsQ0FBQ0MsT0FBZ0IsRUFBRTtJQUMzQyxJQUFJO1FBQ0ZDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG1CQUFtQixDQUFDO1FBRWhDLE1BQU1DLElBQUksR0FBRyxNQUFNSCxPQUFPLENBQUNJLElBQUksQ0FBQyxDQUFDO1FBQ2pDSCxPQUFPLENBQUNDLEdBQUcsQ0FBQyxlQUFlLEVBQUVDLElBQUksQ0FBQztRQUVsQyxNQUFNLEVBQUVFLE1BQU0sRUFBRUMsS0FBSyxFQUFFQyxZQUFZLEVBQUVDLE1BQU0sRUFBRUMsZ0JBQUFBLEVBQWtCLEdBQUdOLElBQUk7UUFFdEU7UUFDQSxJQUFJLENBQUNFLE1BQU0sSUFBSSxDQUFDQyxLQUFLLEVBQUU7WUFDckJMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlCQUF5QixDQUFDO1lBQ3RDLE9BQU9SLHFEQUFZLENBQUNVLElBQUksQ0FBQztnQkFDdkJNLEtBQUssRUFBRTtZQUNULENBQUMsRUFBRTtnQkFBRUMsTUFBTSxFQUFFO1lBQUksQ0FBQyxDQUFDO1FBQ3JCO1FBRUFWLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGtCQUFrQixFQUFFRyxNQUFNLENBQUM7UUFFdkMsSUFBSSxDQUFDUiwrREFBVyxDQUFDUSxNQUFNLENBQUMsRUFBRTtZQUN4QkosT0FBTyxDQUFDQyxHQUFHLENBQUMsa0JBQWtCLEVBQUVHLE1BQU0sQ0FBQztZQUN2QyxPQUFPWCxxREFBWSxDQUFDVSxJQUFJLENBQUM7Z0JBQ3ZCTSxLQUFLLEVBQUU7WUFDVCxDQUFDLEVBQUU7Z0JBQUVDLE1BQU0sRUFBRTtZQUFJLENBQUMsQ0FBQztRQUNyQjtRQUVBLE1BQU1DLElBQUksR0FBR2hCLCtEQUFXLENBQUNTLE1BQU0sQ0FBQztRQUNoQ0osT0FBTyxDQUFDQyxHQUFHLENBQUMsYUFBYSxFQUFFVSxJQUFJLENBQUM7UUFFaEMsSUFBSSxDQUFDQSxJQUFJLEVBQUU7WUFDVFgsT0FBTyxDQUFDQyxHQUFHLENBQUMsZ0JBQWdCLENBQUM7WUFDN0IsT0FBT1IscURBQVksQ0FBQ1UsSUFBSSxDQUFDO2dCQUN2Qk0sS0FBSyxFQUFFO1lBQ1QsQ0FBQyxFQUFFO2dCQUFFQyxNQUFNLEVBQUU7WUFBSSxDQUFDLENBQUM7UUFDckI7UUFFQTtRQUNBLElBQUlOLE1BQU0sS0FBSyxNQUFNLEVBQUU7WUFDckJKLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9DQUFvQyxDQUFDO1lBQ2pELE9BQU9SLHFEQUFZLENBQUNVLElBQUksQ0FBQztnQkFDdkJNLEtBQUssRUFBRTtZQUNULENBQUMsRUFBRTtnQkFBRUMsTUFBTSxFQUFFO1lBQUksQ0FBQyxDQUFDO1FBQ3JCO1FBRUFWLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdDQUFnQyxFQUFFO1lBQUVHLE1BQU07WUFBRUMsS0FBSztZQUFFQztRQUFhLENBQUMsQ0FBQztRQUU5RTtRQUNBLE1BQU1NLE9BQU8sR0FBR0QsSUFBSSxDQUFDRSxhQUFhO1FBRWxDLElBQUksQ0FBQ0QsT0FBTyxFQUFFO1lBQ1paLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGtDQUFrQyxFQUFFRyxNQUFNLENBQUM7WUFDdkQsT0FBT1gscURBQVksQ0FBQ1UsSUFBSSxDQUFDO2dCQUN2Qk0sS0FBSyxFQUFFO1lBQ1QsQ0FBQyxFQUFFO2dCQUFFQyxNQUFNLEVBQUU7WUFBSSxDQUFDLENBQUM7UUFDckI7UUFFQVYsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUJBQWlCLEVBQUVXLE9BQU8sQ0FBQztRQUV2QztRQUNBO1FBQ0EsTUFBTUUsV0FBVyxHQUFHVixNQUFNLEtBQUssS0FBSyxJQUFJQSxNQUFNLEtBQUssU0FBUztRQUM1RCxNQUFNVyxJQUFJLEdBQUdELFdBQVcsR0FBRyxjQUFjLEdBQUcsU0FBUztRQUVyRGQsT0FBTyxDQUFDQyxHQUFHLENBQUMsZUFBZSxFQUFFYyxJQUFJLEVBQUUsV0FBVyxFQUFFWCxNQUFNLENBQUM7UUFFdkQ7UUFDQSxNQUFNWSxhQUFrQixHQUFHO1lBQ3pCQyxvQkFBb0IsRUFBRTtnQkFBQyxNQUFNO2FBQUM7WUFDOUJDLFVBQVUsRUFBRTtnQkFDVjtvQkFDRUMsS0FBSyxFQUFFUCxPQUFPO29CQUNkUSxRQUFRLEVBQUU7Z0JBQ1osQ0FBQzthQUNGO1lBQ0RMLElBQUksRUFBRUEsSUFBSTtZQUNWTSxjQUFjLEVBQUVoQixLQUFLO1lBQ3JCaUIsbUJBQW1CLEVBQUVsQixNQUFNO1lBQzNCbUIsUUFBUSxFQUFFO2dCQUNSbkIsTUFBTSxFQUFFQSxNQUFNO2dCQUNkb0IsYUFBYSxFQUFFbkIsS0FBSztnQkFDcEJDLFlBQVksRUFBRUEsWUFBWSxJQUFJLEVBQUU7Z0JBQ2hDQyxNQUFNLEVBQUVBLE1BQU0sSUFBSSxFQUFFO2dCQUFFO2dCQUN0QkMsZ0JBQWdCLEVBQUVBLGdCQUFnQixHQUFHaUIsSUFBSSxDQUFDQyxTQUFTLENBQUNsQixnQkFBZ0IsQ0FBQyxHQUFHLEVBQUU7Z0JBQUU7Z0JBQzVFbUIsU0FBUyxFQUFFLElBQUlDLElBQUksQ0FBQyxDQUFDLENBQUNDLFdBQVcsQ0FBQyxDQUFDO2dCQUNuQ0MsTUFBTSxFQUFFLGlCQUFpQjtnQkFDekJDLFlBQVksRUFBRTtZQUNoQixDQUFDO1lBQ0RDLFdBQVcsRUFBRyxHQUFFbkMsd0RBQVEsQ0FBQ29DLE9BQVEsMENBQXlDN0IsTUFBTyxFQUFDO1lBQ2xGOEIsVUFBVSxFQUFHLEdBQUVyQyx3REFBUSxDQUFDc0MsTUFBTyxTQUFRL0IsTUFBTyxFQUFDO1lBQy9DZ0MsYUFBYSxFQUFFO2dCQUNiQyxPQUFPLEVBQUU7WUFDWCxDQUFDO1lBQ0RDLDBCQUEwQixFQUFFLFVBQVU7WUFDdENDLHFCQUFxQixFQUFFO1FBQ3pCLENBQUM7UUFFRDtRQUNBLElBQUl6QixXQUFXLEVBQUU7WUFDZkUsYUFBYSxDQUFDd0IsaUJBQWlCLEdBQUc7Z0JBQ2hDakIsUUFBUSxFQUFFO29CQUNSbkIsTUFBTSxFQUFFQSxNQUFNO29CQUNkb0IsYUFBYSxFQUFFbkIsS0FBSztvQkFDcEJDLFlBQVksRUFBRUEsWUFBWSxJQUFJLEVBQUU7b0JBQ2hDQyxNQUFNLEVBQUVBLE1BQU0sSUFBSSxFQUFFO29CQUFFO29CQUN0QkMsZ0JBQWdCLEVBQUVBLGdCQUFnQixHQUFHaUIsSUFBSSxDQUFDQyxTQUFTLENBQUNsQixnQkFBZ0IsQ0FBQyxHQUFHLEVBQUU7b0JBQUU7b0JBQzVFc0IsTUFBTSxFQUFFLGlCQUFpQjtvQkFDekJDLFlBQVksRUFBRTtnQkFDaEI7WUFFRixDQUFDO1FBQ0g7UUFFQSxJQUFJLENBQUNyQyxzREFBTSxFQUFFO1lBQ1hNLE9BQU8sQ0FBQ1MsS0FBSyxDQUFDLHdCQUF3QixDQUFDO1lBQ3ZDLE9BQU9oQixxREFBWSxDQUFDVSxJQUFJLENBQUM7Z0JBQ3ZCTSxLQUFLLEVBQUU7WUFDVCxDQUFDLEVBQUU7Z0JBQUVDLE1BQU0sRUFBRTtZQUFJLENBQUMsQ0FBQztRQUNyQjtRQUVBLE1BQU0rQixPQUFPLEdBQUcsTUFBTS9DLHNEQUFNLENBQUNnRCxRQUFRLENBQUNDLFFBQVEsQ0FBQ0MsTUFBTSxDQUFDNUIsYUFBYSxDQUFDO1FBRXBFaEIsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkJBQTJCLEVBQUV3QyxPQUFPLENBQUNJLEVBQUUsQ0FBQztRQUVwRCxPQUFPcEQscURBQVksQ0FBQ1UsSUFBSSxDQUFDO1lBQ3ZCMkMsU0FBUyxFQUFFTCxPQUFPLENBQUNJLEVBQUU7WUFDckJFLEdBQUcsRUFBRU4sT0FBTyxDQUFDTSxHQUFBQTtRQUNmLENBQUMsQ0FBQztJQUVKLENBQUMsQ0FBQyxPQUFPdEMsS0FBSyxFQUFFO1FBQ2RULE9BQU8sQ0FBQ1MsS0FBSyxDQUFDLGtDQUFrQyxFQUFFQSxLQUFLLENBQUM7UUFDeERULE9BQU8sQ0FBQ1MsS0FBSyxDQUFDLGNBQWMsRUFBRUEsS0FBSyxZQUFZdUMsS0FBSyxHQUFHdkMsS0FBSyxDQUFDd0MsS0FBSyxHQUFHLGdCQUFnQixDQUFDO1FBRXRGLElBQUlDLFlBQVksR0FBRyxrQ0FBa0M7UUFDckQsSUFBSXpDLEtBQUssWUFBWXVDLEtBQUssRUFBRTtZQUMxQkUsWUFBWSxHQUFHekMsS0FBSyxDQUFDMEMsT0FBTztZQUM1Qm5ELE9BQU8sQ0FBQ1MsS0FBSyxDQUFDLGdCQUFnQixFQUFFeUMsWUFBWSxDQUFDO1FBQy9DO1FBRUEsT0FBT3pELHFEQUFZLENBQUNVLElBQUksQ0FBQztZQUN2Qk0sS0FBSyxFQUFFeUMsWUFBWTtZQUNuQkUsT0FBTyxFQUFFM0MsS0FBSyxZQUFZdUMsS0FBSyxHQUFHdkMsS0FBSyxDQUFDMEMsT0FBTyxHQUFHO1FBQ3BELENBQUMsRUFBRTtZQUFFekMsTUFBTSxFQUFFO1FBQUksQ0FBQyxDQUFDO0lBQ3JCO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcc3JjXFxhcHBcXGFwaVxcc3RyaXBlXFxjcmVhdGUtY2hlY2tvdXQtc2Vzc2lvblxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2FwcC9hcGkvc3RyaXBlL2NyZWF0ZS1jaGVja291dC1zZXNzaW9uL3JvdXRlLnRzXG5pbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBzdHJpcGUsIGdldFBsYW5CeUlkLCBpc1ZhbGlkUGxhbiwgQVBQX1VSTFMgfSBmcm9tICdAL2xpYi9zdHJpcGUvY29uZmlnJztcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogUmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCdTdHJpcGUgQVBJIGNhbGxlZCcpO1xuICAgIFxuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBjb25zb2xlLmxvZygnUmVxdWVzdCBib2R5OicsIGJvZHkpO1xuICAgIFxuICAgIGNvbnN0IHsgcGxhbklkLCBlbWFpbCwgY3VzdG9tZXJOYW1lLCB1c2VySWQsIHJlZ2lzdHJhdGlvbkRhdGEgfSA9IGJvZHk7XG5cbiAgICAvLyBWYWxpZGFjaW9uZXMgYsOhc2ljYXNcbiAgICBpZiAoIXBsYW5JZCB8fCAhZW1haWwpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdNaXNzaW5nIHBsYW5JZCBvciBlbWFpbCcpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgZXJyb3I6ICdQbGFuIElEIHkgZW1haWwgc29uIHJlcXVlcmlkb3MnXG4gICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdWYWxpZGF0aW5nIHBsYW46JywgcGxhbklkKTtcbiAgICBcbiAgICBpZiAoIWlzVmFsaWRQbGFuKHBsYW5JZCkpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdJbnZhbGlkIHBsYW4gSUQ6JywgcGxhbklkKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIGVycm9yOiAnUGxhbiBJRCBubyB2w6FsaWRvJ1xuICAgICAgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICBjb25zdCBwbGFuID0gZ2V0UGxhbkJ5SWQocGxhbklkKTtcbiAgICBjb25zb2xlLmxvZygnUGxhbiBmb3VuZDonLCBwbGFuKTtcbiAgICBcbiAgICBpZiAoIXBsYW4pIHtcbiAgICAgIGNvbnNvbGUubG9nKCdQbGFuIG5vdCBmb3VuZCcpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgZXJyb3I6ICdQbGFuIG5vIGVuY29udHJhZG8nXG4gICAgICB9LCB7IHN0YXR1czogNDA0IH0pO1xuICAgIH1cblxuICAgIC8vIEVsIHBsYW4gZ3JhdHVpdG8gbm8gcmVxdWllcmUgcGFnb1xuICAgIGlmIChwbGFuSWQgPT09ICdmcmVlJykge1xuICAgICAgY29uc29sZS5sb2coJ0ZyZWUgcGxhbiBkb2VzIG5vdCByZXF1aXJlIHBheW1lbnQnKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIGVycm9yOiAnRWwgcGxhbiBncmF0dWl0byBubyByZXF1aWVyZSBwYWdvJ1xuICAgICAgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgY2hlY2tvdXQgc2Vzc2lvbiBmb3I6JywgeyBwbGFuSWQsIGVtYWlsLCBjdXN0b21lck5hbWUgfSk7XG5cbiAgICAvLyBVc2FyIGVsIHByZWNpbyBmaWpvIGNvbmZpZ3VyYWRvXG4gICAgY29uc3QgcHJpY2VJZCA9IHBsYW4uc3RyaXBlUHJpY2VJZDtcbiAgICBcbiAgICBpZiAoIXByaWNlSWQpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdObyBwcmljZSBJRCBjb25maWd1cmVkIGZvciBwbGFuOicsIHBsYW5JZCk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBlcnJvcjogJ1ByZWNpbyBubyBjb25maWd1cmFkbyBwYXJhIGVzdGUgcGxhbidcbiAgICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ1VzaW5nIHByaWNlIElEOicsIHByaWNlSWQpO1xuXG4gICAgLy8gRGV0ZXJtaW5hciBlbCBtb2RvIGRlIHBhZ28gYmFzYWRvIGVuIGVsIHBsYW5cbiAgICAvLyBBaG9yYSAndXN1YXJpbycgeSAncHJvJyBzb24gcmVjdXJyZW50ZXMgKHN1c2NyaXBjaW9uZXMgbWVuc3VhbGVzKVxuICAgIGNvbnN0IGlzUmVjdXJyaW5nID0gcGxhbklkID09PSAncHJvJyB8fCBwbGFuSWQgPT09ICd1c3VhcmlvJztcbiAgICBjb25zdCBtb2RlID0gaXNSZWN1cnJpbmcgPyAnc3Vic2NyaXB0aW9uJyA6ICdwYXltZW50JztcblxuICAgIGNvbnNvbGUubG9nKCdQYXltZW50IG1vZGU6JywgbW9kZSwgJ2ZvciBwbGFuOicsIHBsYW5JZCk7XG5cbiAgICAvLyBDcmVhciBzZXNpw7NuIGRlIGNoZWNrb3V0XG4gICAgY29uc3Qgc2Vzc2lvbkNvbmZpZzogYW55ID0ge1xuICAgICAgcGF5bWVudF9tZXRob2RfdHlwZXM6IFsnY2FyZCddLFxuICAgICAgbGluZV9pdGVtczogW1xuICAgICAgICB7XG4gICAgICAgICAgcHJpY2U6IHByaWNlSWQsXG4gICAgICAgICAgcXVhbnRpdHk6IDEsXG4gICAgICAgIH0sXG4gICAgICBdLFxuICAgICAgbW9kZTogbW9kZSxcbiAgICAgIGN1c3RvbWVyX2VtYWlsOiBlbWFpbCxcbiAgICAgIGNsaWVudF9yZWZlcmVuY2VfaWQ6IHBsYW5JZCxcbiAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgIHBsYW5JZDogcGxhbklkLFxuICAgICAgICBjdXN0b21lckVtYWlsOiBlbWFpbCxcbiAgICAgICAgY3VzdG9tZXJOYW1lOiBjdXN0b21lck5hbWUgfHwgJycsXG4gICAgICAgIHVzZXJJZDogdXNlcklkIHx8ICcnLCAvLyBJRCBkZWwgdXN1YXJpbyBwcmUtcmVnaXN0cmFkbyAodmFjw61vIHBhcmEgbnVldm8gZmx1am8pXG4gICAgICAgIHJlZ2lzdHJhdGlvbkRhdGE6IHJlZ2lzdHJhdGlvbkRhdGEgPyBKU09OLnN0cmluZ2lmeShyZWdpc3RyYXRpb25EYXRhKSA6ICcnLCAvLyBEYXRvcyBkZSByZWdpc3RybyBwYXJhIGNyZWFyIGN1ZW50YSBkZXNwdcOpcyBkZWwgcGFnb1xuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgc291cmNlOiAnb3Bvc2lhaV93ZWJzaXRlJyxcbiAgICAgICAgYXV0b0FjdGl2YXRlOiAndHJ1ZSdcbiAgICAgIH0sXG4gICAgICBzdWNjZXNzX3VybDogYCR7QVBQX1VSTFMuc3VjY2Vzc30/c2Vzc2lvbl9pZD17Q0hFQ0tPVVRfU0VTU0lPTl9JRH0mcGxhbj0ke3BsYW5JZH1gLFxuICAgICAgY2FuY2VsX3VybDogYCR7QVBQX1VSTFMuY2FuY2VsfT9wbGFuPSR7cGxhbklkfWAsXG4gICAgICBhdXRvbWF0aWNfdGF4OiB7XG4gICAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICB9LFxuICAgICAgYmlsbGluZ19hZGRyZXNzX2NvbGxlY3Rpb246ICdyZXF1aXJlZCcsXG4gICAgICBhbGxvd19wcm9tb3Rpb25fY29kZXM6IHRydWUsXG4gICAgfTtcblxuICAgIC8vIEFncmVnYXIgY29uZmlndXJhY2nDs24gZXNwZWPDrWZpY2EgcGFyYSBzdXNjcmlwY2lvbmVzIChBSE9SQSBBUExJQ0EgQSAndXN1YXJpbycgWSAncHJvJylcbiAgICBpZiAoaXNSZWN1cnJpbmcpIHtcbiAgICAgIHNlc3Npb25Db25maWcuc3Vic2NyaXB0aW9uX2RhdGEgPSB7XG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgICAgcGxhbklkOiBwbGFuSWQsXG4gICAgICAgICAgY3VzdG9tZXJFbWFpbDogZW1haWwsXG4gICAgICAgICAgY3VzdG9tZXJOYW1lOiBjdXN0b21lck5hbWUgfHwgJycsXG4gICAgICAgICAgdXNlcklkOiB1c2VySWQgfHwgJycsIC8vIElEIGRlbCB1c3VhcmlvIHByZS1yZWdpc3RyYWRvICh2YWPDrW8gcGFyYSBudWV2byBmbHVqbylcbiAgICAgICAgICByZWdpc3RyYXRpb25EYXRhOiByZWdpc3RyYXRpb25EYXRhID8gSlNPTi5zdHJpbmdpZnkocmVnaXN0cmF0aW9uRGF0YSkgOiAnJywgLy8gRGF0b3MgZGUgcmVnaXN0cm8gcGFyYSBjcmVhciBjdWVudGEgZGVzcHXDqXMgZGVsIHBhZ29cbiAgICAgICAgICBzb3VyY2U6ICdvcG9zaWFpX3dlYnNpdGUnLFxuICAgICAgICAgIGF1dG9BY3RpdmF0ZTogJ3RydWUnXG4gICAgICAgIH0sXG4gICAgICAgIC8vIHRyaWFsX3BlcmlvZF9kYXlzOiA3LCAvLyBPcGNpb25hbDogc2kgcXVpZXJlcyBvZnJlY2VyIHVuIHBlcmlvZG8gZGUgcHJ1ZWJhXG4gICAgICB9O1xuICAgIH1cblxuICAgIGlmICghc3RyaXBlKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTdHJpcGUgbm90IGluaXRpYWxpemVkJyk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBlcnJvcjogJ0Vycm9yIGRlIGNvbmZpZ3VyYWNpw7NuIGRlIFN0cmlwZSdcbiAgICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gICAgfVxuXG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IHN0cmlwZS5jaGVja291dC5zZXNzaW9ucy5jcmVhdGUoc2Vzc2lvbkNvbmZpZyk7XG5cbiAgICBjb25zb2xlLmxvZygnQ2hlY2tvdXQgc2Vzc2lvbiBjcmVhdGVkOicsIHNlc3Npb24uaWQpO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHNlc3Npb25JZDogc2Vzc2lvbi5pZCxcbiAgICAgIHVybDogc2Vzc2lvbi51cmxcbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGNoZWNrb3V0IHNlc3Npb246JywgZXJyb3IpO1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN0YWNrOicsIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5zdGFjayA6ICdObyBzdGFjayB0cmFjZScpO1xuICAgIFxuICAgIGxldCBlcnJvck1lc3NhZ2UgPSAnRXJyb3IgYWwgY3JlYXIgbGEgc2VzacOzbiBkZSBwYWdvJztcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IubWVzc2FnZTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIG1lc3NhZ2U6JywgZXJyb3JNZXNzYWdlKTtcbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgZXJyb3I6IGVycm9yTWVzc2FnZSxcbiAgICAgIGRldGFpbHM6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsInN0cmlwZSIsImdldFBsYW5CeUlkIiwiaXNWYWxpZFBsYW4iLCJBUFBfVVJMUyIsIlBPU1QiLCJyZXF1ZXN0IiwiY29uc29sZSIsImxvZyIsImJvZHkiLCJqc29uIiwicGxhbklkIiwiZW1haWwiLCJjdXN0b21lck5hbWUiLCJ1c2VySWQiLCJyZWdpc3RyYXRpb25EYXRhIiwiZXJyb3IiLCJzdGF0dXMiLCJwbGFuIiwicHJpY2VJZCIsInN0cmlwZVByaWNlSWQiLCJpc1JlY3VycmluZyIsIm1vZGUiLCJzZXNzaW9uQ29uZmlnIiwicGF5bWVudF9tZXRob2RfdHlwZXMiLCJsaW5lX2l0ZW1zIiwicHJpY2UiLCJxdWFudGl0eSIsImN1c3RvbWVyX2VtYWlsIiwiY2xpZW50X3JlZmVyZW5jZV9pZCIsIm1ldGFkYXRhIiwiY3VzdG9tZXJFbWFpbCIsIkpTT04iLCJzdHJpbmdpZnkiLCJjcmVhdGVkQXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzb3VyY2UiLCJhdXRvQWN0aXZhdGUiLCJzdWNjZXNzX3VybCIsInN1Y2Nlc3MiLCJjYW5jZWxfdXJsIiwiY2FuY2VsIiwiYXV0b21hdGljX3RheCIsImVuYWJsZWQiLCJiaWxsaW5nX2FkZHJlc3NfY29sbGVjdGlvbiIsImFsbG93X3Byb21vdGlvbl9jb2RlcyIsInN1YnNjcmlwdGlvbl9kYXRhIiwic2Vzc2lvbiIsImNoZWNrb3V0Iiwic2Vzc2lvbnMiLCJjcmVhdGUiLCJpZCIsInNlc3Npb25JZCIsInVybCIsIkVycm9yIiwic3RhY2siLCJlcnJvck1lc3NhZ2UiLCJtZXNzYWdlIiwiZGV0YWlscyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/create-checkout-session/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe/config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe/config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.APP_URLS),\n/* harmony export */   PLANS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.PLANS),\n/* harmony export */   getPlanById: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.getPlanById),\n/* harmony export */   isValidPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.isValidPlan),\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plans */ \"(rsc)/./src/lib/stripe/plans.ts\");\n// src/lib/stripe/config.ts\n\n// Inicializar Stripe solo en el servidor\nconst stripe =  true ? new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: '2025-05-28.basil',\n    typescript: true\n}) : 0;\n// Importar configuración de planes\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS9jb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBO0FBQzJCO0FBRTNCO0FBQ08sTUFBTUMsTUFBTSxHQUFHLFFBQ2xCLElBQUlELDhDQUFNLENBQUNFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxpQkFBaUIsRUFBRztJQUN6Q0MsVUFBVSxFQUFFLGtCQUFrQjtJQUM5QkMsVUFBVSxFQUFFO0FBQ2QsQ0FBQyxDQUFDLEdBQ0YsQ0FBSTtBQUVSO0FBQ21FIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFxzdHJpcGVcXGNvbmZpZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvbGliL3N0cmlwZS9jb25maWcudHNcbmltcG9ydCBTdHJpcGUgZnJvbSAnc3RyaXBlJztcblxuLy8gSW5pY2lhbGl6YXIgU3RyaXBlIHNvbG8gZW4gZWwgc2Vydmlkb3JcbmV4cG9ydCBjb25zdCBzdHJpcGUgPSB0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJ1xuICA/IG5ldyBTdHJpcGUocHJvY2Vzcy5lbnYuU1RSSVBFX1NFQ1JFVF9LRVkhLCB7XG4gICAgICBhcGlWZXJzaW9uOiAnMjAyNS0wNS0yOC5iYXNpbCcsXG4gICAgICB0eXBlc2NyaXB0OiB0cnVlLFxuICAgIH0pXG4gIDogbnVsbDtcblxuLy8gSW1wb3J0YXIgY29uZmlndXJhY2nDs24gZGUgcGxhbmVzXG5leHBvcnQgeyBQTEFOUywgZ2V0UGxhbkJ5SWQsIGlzVmFsaWRQbGFuLCBBUFBfVVJMUyB9IGZyb20gJy4vcGxhbnMnO1xuZXhwb3J0IHR5cGUgeyBQbGFuSWQgfSBmcm9tICcuL3BsYW5zJztcbiJdLCJuYW1lcyI6WyJTdHJpcGUiLCJzdHJpcGUiLCJwcm9jZXNzIiwiZW52IiwiU1RSSVBFX1NFQ1JFVF9LRVkiLCJhcGlWZXJzaW9uIiwidHlwZXNjcmlwdCIsIlBMQU5TIiwiZ2V0UGxhbkJ5SWQiLCJpc1ZhbGlkUGxhbiIsIkFQUF9VUkxTIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // En centavos (€10.00)\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // En centavos (€15.00)\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de Resúmenes para A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return plan?.planConfig || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        // En centavos (€10.00)\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        // Placeholder - crear en Stripe\n        stripePriceId: 'price_tokens_additional' // Placeholder - crear en Stripe\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: `${\"http://localhost:3000\"}/thank-you`,\n    cancel: `${\"http://localhost:3000\"}/upgrade-plan`,\n    webhook: `${\"http://localhost:3000\"}/api/stripe/webhook`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe/plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            // Límite total para el trial de 5 días\n            testsForTrial: 10,\n            // Límite total para el trial de 5 días\n            flashcardsForTrial: 10,\n            // Límite total para el trial de 5 días\n            tokensForTrial: 50000,\n            // Límite total para el trial de 5 días\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // €10.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // €15.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzL3BsYW5MaW1pdHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQXdCQTtBQUNPLE1BQU1BLG1CQUFzRCxHQUFHO0lBQ3BFQyxJQUFJLEVBQUU7UUFDSkMsRUFBRSxFQUFFLE1BQU07UUFDVkMsSUFBSSxFQUFFLGFBQWE7UUFDbkJDLEtBQUssRUFBRSxDQUFDO1FBQ1JDLE1BQU0sRUFBRTtZQUNOQyxTQUFTLEVBQUUsQ0FBQztZQUNaQyxnQkFBZ0IsRUFBRSxDQUFDO1lBQVM7WUFDNUJDLGFBQWEsRUFBRSxFQUFFO1lBQVc7WUFDNUJDLGtCQUFrQixFQUFFLEVBQUU7WUFBTTtZQUM1QkMsY0FBYyxFQUFFLEtBQUs7WUFBTztZQUM1QkMsUUFBUSxFQUFFO2dCQUFDLGlCQUFpQjtnQkFBRSxpQkFBaUI7Z0JBQUUsc0JBQXNCO2dCQUFFLHFCQUFxQjthQUFBO1FBQ2hHLENBQUM7UUFDREEsUUFBUSxFQUFFO1lBQ1IsaUJBQWlCO1lBQ2pCLGlCQUFpQjtZQUNqQixzQkFBc0I7WUFDdEIscUJBQXFCO1NBQ3RCO1FBQ0RDLGtCQUFrQixFQUFFO1lBQ2xCLGdCQUFnQjtZQUNoQixlQUFlO1lBQ2YsZUFBZTtTQUFBO0lBRW5CLENBQUM7SUFDREMsT0FBTyxFQUFFO1FBQ1BYLEVBQUUsRUFBRSxTQUFTO1FBQ2JDLElBQUksRUFBRSxjQUFjO1FBQ3BCQyxLQUFLLEVBQUUsSUFBSTtRQUFFO1FBQ2JDLE1BQU0sRUFBRTtZQUNOQyxTQUFTLEVBQUUsQ0FBQyxDQUFDO1lBQUU7WUFDZlEsZUFBZSxFQUFFLENBQUMsQ0FBQztZQUFTO1lBQzVCQyxZQUFZLEVBQUUsQ0FBQyxDQUFDO1lBQVk7WUFDNUJDLGlCQUFpQixFQUFFLENBQUMsQ0FBQztZQUFPO1lBQzVCQyxhQUFhLEVBQUUsT0FBTztZQUFNO1lBQzVCTixRQUFRLEVBQUU7Z0JBQUMsaUJBQWlCO2dCQUFFLGVBQWU7Z0JBQUUsaUJBQWlCO2dCQUFFLHNCQUFzQjtnQkFBRSxxQkFBcUI7YUFBQTtRQUNqSCxDQUFDO1FBQ0RBLFFBQVEsRUFBRTtZQUNSLGlCQUFpQjtZQUNqQixlQUFlO1lBQ2YsaUJBQWlCO1lBQ2pCLHNCQUFzQjtZQUN0QixxQkFBcUI7U0FDdEI7UUFDREMsa0JBQWtCLEVBQUU7WUFDbEIsZ0JBQWdCO1lBQ2hCLGVBQWU7U0FBQTtJQUVuQixDQUFDO0lBQ0RNLEdBQUcsRUFBRTtRQUNIaEIsRUFBRSxFQUFFLEtBQUs7UUFDVEMsSUFBSSxFQUFFLFVBQVU7UUFDaEJDLEtBQUssRUFBRSxJQUFJO1FBQUU7UUFDYkMsTUFBTSxFQUFFO1lBQ05DLFNBQVMsRUFBRSxDQUFDLENBQUM7WUFBRTtZQUNmUSxlQUFlLEVBQUUsQ0FBQyxDQUFDO1lBQVM7WUFDNUJDLFlBQVksRUFBRSxDQUFDLENBQUM7WUFBWTtZQUM1QkMsaUJBQWlCLEVBQUUsQ0FBQyxDQUFDO1lBQU87WUFDNUJDLGFBQWEsRUFBRSxPQUFPO1lBQU07WUFDNUJOLFFBQVEsRUFBRTtnQkFBQyxpQkFBaUI7Z0JBQUUsZ0JBQWdCO2dCQUFFLGVBQWU7Z0JBQUUsaUJBQWlCO2dCQUFFLHNCQUFzQjtnQkFBRSxxQkFBcUI7Z0JBQUUsZUFBZTthQUFBO1FBQ3BKLENBQUM7UUFDREEsUUFBUSxFQUFFO1lBQ1IsaUJBQWlCO1lBQ2pCLGdCQUFnQjtZQUNoQixlQUFlO1lBQ2YsaUJBQWlCO1lBQ2pCLHNCQUFzQjtZQUN0QixxQkFBcUI7WUFDckIsZUFBZTtTQUNoQjtRQUNEQyxrQkFBa0IsRUFBRTtJQUN0QjtBQUNGLENBQUM7QUFFRDtBQUNPLFNBQVNPLG9CQUFvQkEsQ0FBQ0MsTUFBYyxFQUE0QjtJQUM3RSxPQUFPcEIsbUJBQW1CLENBQUNvQixNQUFNLENBQUMsSUFBSSxJQUFJO0FBQzVDO0FBRU8sU0FBU0Msb0JBQW9CQSxDQUFDRCxNQUFjLEVBQVU7SUFDM0QsTUFBTUUsTUFBTSxHQUFHSCxvQkFBb0IsQ0FBQ0MsTUFBTSxDQUFDO0lBQzNDLElBQUksQ0FBQ0UsTUFBTSxFQUFFLE9BQU8sS0FBSztJQUV6QjtJQUNBLElBQUlGLE1BQU0sS0FBSyxNQUFNLEVBQUU7UUFDckIsT0FBT0UsTUFBTSxDQUFDakIsTUFBTSxDQUFDSyxjQUFjLElBQUksS0FBSztJQUM5QztJQUVBO0lBQ0EsT0FBT1ksTUFBTSxDQUFDakIsTUFBTSxDQUFDWSxhQUFhLElBQUksT0FBTztBQUMvQztBQUVPLFNBQVNNLGdCQUFnQkEsQ0FBQ0gsTUFBYyxFQUFFSSxPQUFlLEVBQVc7SUFDekUsTUFBTUYsTUFBTSxHQUFHSCxvQkFBb0IsQ0FBQ0MsTUFBTSxDQUFDO0lBQzNDLElBQUksQ0FBQ0UsTUFBTSxFQUFFLE9BQU8sS0FBSztJQUV6QjtJQUNBLElBQUlBLE1BQU0sQ0FBQ1Ysa0JBQWtCLENBQUNhLFFBQVEsQ0FBQ0QsT0FBTyxDQUFDLEVBQUU7UUFDL0MsT0FBTyxLQUFLO0lBQ2Q7SUFFQTtJQUNBLE9BQU9GLE1BQU0sQ0FBQ1gsUUFBUSxDQUFDYyxRQUFRLENBQUNELE9BQU8sQ0FBQztBQUMxQztBQUVPLFNBQVNFLGNBQWNBLENBQUNOLE1BQWMsRUFBRU8sU0FBOEMsRUFBVTtJQUNyRyxNQUFNTCxNQUFNLEdBQUdILG9CQUFvQixDQUFDQyxNQUFNLENBQUM7SUFDM0MsSUFBSSxDQUFDRSxNQUFNLEVBQUUsT0FBTyxDQUFDO0lBRXJCLE9BQVFLLFNBQVM7UUFDZixLQUFLLFVBQVU7WUFDYixPQUFPTCxNQUFNLENBQUNqQixNQUFNLENBQUNTLGVBQWUsSUFBSSxDQUFDO1FBQzNDLEtBQUssT0FBTztZQUNWLE9BQU9RLE1BQU0sQ0FBQ2pCLE1BQU0sQ0FBQ1UsWUFBWSxJQUFJLENBQUM7UUFDeEMsS0FBSyxZQUFZO1lBQ2YsT0FBT08sTUFBTSxDQUFDakIsTUFBTSxDQUFDVyxpQkFBaUIsSUFBSSxDQUFDO1FBQzdDO1lBQ0UsT0FBTyxDQUFDO0lBQ1o7QUFDRjtBQUVBO0FBQ08sU0FBU1ksYUFBYUEsQ0FBQ1IsTUFBYyxFQUFFTyxTQUF5RCxFQUFVO0lBQy9HLE1BQU1MLE1BQU0sR0FBR0gsb0JBQW9CLENBQUNDLE1BQU0sQ0FBQztJQUMzQyxJQUFJLENBQUNFLE1BQU0sSUFBSUYsTUFBTSxLQUFLLE1BQU0sRUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFN0MsT0FBUU8sU0FBUztRQUNmLEtBQUssVUFBVTtZQUNiLE9BQU9MLE1BQU0sQ0FBQ2pCLE1BQU0sQ0FBQ0UsZ0JBQWdCLElBQUksQ0FBQztRQUM1QyxLQUFLLE9BQU87WUFDVixPQUFPZSxNQUFNLENBQUNqQixNQUFNLENBQUNHLGFBQWEsSUFBSSxDQUFDO1FBQ3pDLEtBQUssWUFBWTtZQUNmLE9BQU9jLE1BQU0sQ0FBQ2pCLE1BQU0sQ0FBQ0ksa0JBQWtCLElBQUksQ0FBQztRQUM5QyxLQUFLLFFBQVE7WUFDWCxPQUFPYSxNQUFNLENBQUNqQixNQUFNLENBQUNLLGNBQWMsSUFBSSxDQUFDO1FBQzFDO1lBQ0UsT0FBTyxDQUFDO0lBQ1o7QUFDRjtBQUVPLFNBQVNtQixXQUFXQSxDQUFDQyxLQUFhLEVBQVc7SUFDbEQsT0FBT0EsS0FBSyxLQUFLLENBQUMsQ0FBQztBQUNyQjtBQUVBO0FBQ08sU0FBU0MsZ0JBQWdCQSxDQUM5QlgsTUFBYyxFQUNkSSxPQUFlLEVBQ2ZRLFlBQW9CLEVBQ3BCTCxTQUErQyxFQUNSO0lBQ3ZDLE1BQU1MLE1BQU0sR0FBR0gsb0JBQW9CLENBQUNDLE1BQU0sQ0FBQztJQUUzQyxJQUFJLENBQUNFLE1BQU0sRUFBRTtRQUNYLE9BQU87WUFBRVcsT0FBTyxFQUFFLEtBQUs7WUFBRUMsTUFBTSxFQUFFO1FBQWlCLENBQUM7SUFDckQ7SUFFQTtJQUNBLElBQUksQ0FBQ1gsZ0JBQWdCLENBQUNILE1BQU0sRUFBRUksT0FBTyxDQUFDLEVBQUU7UUFDdEMsT0FBTztZQUFFUyxPQUFPLEVBQUUsS0FBSztZQUFFQyxNQUFNLEVBQUcsa0JBQWlCVixPQUFRLHFCQUFvQkYsTUFBTSxDQUFDbkIsSUFBSztRQUFFLENBQUM7SUFDaEc7SUFFQTtJQUNBLElBQUl3QixTQUFTLEVBQUU7UUFDYixNQUFNUSxXQUFXLEdBQUdULGNBQWMsQ0FBQ04sTUFBTSxFQUFFTyxTQUFTLENBQUM7UUFDckQsSUFBSSxDQUFDRSxXQUFXLENBQUNNLFdBQVcsQ0FBQyxJQUFJSCxZQUFZLElBQUlHLFdBQVcsRUFBRTtZQUM1RCxPQUFPO2dCQUFFRixPQUFPLEVBQUUsS0FBSztnQkFBRUMsTUFBTSxFQUFHLHFCQUFvQlAsU0FBVSxlQUFjUSxXQUFZO1lBQUcsQ0FBQztRQUNoRztJQUNGO0lBRUEsT0FBTztRQUFFRixPQUFPLEVBQUU7SUFBSyxDQUFDO0FBQzFCO0FBRUE7QUFDTyxlQUFlRyxzQkFBc0JBLENBQUNaLE9BQWUsRUFBb0I7SUFDOUUsSUFBSTtRQUNGO1FBQ0EsTUFBTWEsUUFBUSxHQUFHLE1BQU1DLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQztRQUM5QyxJQUFJLENBQUNELFFBQVEsQ0FBQ0UsRUFBRSxFQUFFO1lBQ2hCQyxPQUFPLENBQUNDLEtBQUssQ0FBQyxtQ0FBbUMsQ0FBQztZQUNsRDtZQUNBLE9BQU9sQixnQkFBZ0IsQ0FBQyxNQUFNLEVBQUVDLE9BQU8sQ0FBQztRQUMxQztRQUVBLE1BQU0sRUFBRWtCLElBQUFBLEVBQU0sR0FBRyxNQUFNTCxRQUFRLENBQUNNLElBQUksQ0FBQyxDQUFDO1FBQ3RDLE1BQU1DLFFBQVEsR0FBR0YsSUFBSSxJQUFJLE1BQU07UUFFL0I7UUFDQSxPQUFPbkIsZ0JBQWdCLENBQUNxQixRQUFRLEVBQUVwQixPQUFPLENBQUM7SUFDNUMsQ0FBQyxDQUFDLE9BQU9pQixLQUFLLEVBQUU7UUFDZEQsT0FBTyxDQUFDQyxLQUFLLENBQUMsNENBQTRDLEVBQUVBLEtBQUssQ0FBQztRQUNsRTtRQUNBLE9BQU9sQixnQkFBZ0IsQ0FBQyxNQUFNLEVBQUVDLE9BQU8sQ0FBQztJQUMxQztBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFx1dGlsc1xccGxhbkxpbWl0cy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvbGliL3V0aWxzL3BsYW5MaW1pdHMudHNcbi8vIENvbmZpZ3VyYWNpw7NuIGNlbnRyYWxpemFkYSBkZSBsw61taXRlcyB5IGNhcmFjdGVyw61zdGljYXMgcG9yIHBsYW5cblxuZXhwb3J0IGludGVyZmFjZSBQbGFuTGltaXRzIHtcbiAgZG9jdW1lbnRzOiBudW1iZXI7IC8vIC0xID0gaWxpbWl0YWRvXG4gIG1pbmRNYXBzUGVyV2Vlaz86IG51bWJlcjsgICAgICAvLyBQYXJhIHBsYW5lcyBkZSBwYWdvIGNvbiBsw61taXRlcyBzZW1hbmFsZXNcbiAgdGVzdHNQZXJXZWVrPzogbnVtYmVyOyAgICAgICAgIC8vIFBhcmEgcGxhbmVzIGRlIHBhZ28gY29uIGzDrW1pdGVzIHNlbWFuYWxlc1xuICBmbGFzaGNhcmRzUGVyV2Vlaz86IG51bWJlcjsgICAgLy8gUGFyYSBwbGFuZXMgZGUgcGFnbyBjb24gbMOtbWl0ZXMgc2VtYW5hbGVzXG4gIG1vbnRobHlUb2tlbnM/OiBudW1iZXI7ICAgICAgICAvLyBQYXJhIHBsYW5lcyBkZSBwYWdvIGNvbiBsw61taXRlcyBtZW5zdWFsZXNcbiAgbWluZE1hcHNGb3JUcmlhbD86IG51bWJlcjsgICAgIC8vIEVzcGVjw61maWNvIHBhcmEgdHJpYWwgZGUgNSBkw61hc1xuICB0ZXN0c0ZvclRyaWFsPzogbnVtYmVyOyAgICAgICAgLy8gRXNwZWPDrWZpY28gcGFyYSB0cmlhbCBkZSA1IGTDrWFzXG4gIGZsYXNoY2FyZHNGb3JUcmlhbD86IG51bWJlcjsgICAvLyBFc3BlY8OtZmljbyBwYXJhIHRyaWFsIGRlIDUgZMOtYXNcbiAgdG9rZW5zRm9yVHJpYWw/OiBudW1iZXI7ICAgICAgIC8vIEVzcGVjw61maWNvIHBhcmEgdHJpYWwgZGUgNSBkw61hc1xuICBmZWF0dXJlczogc3RyaW5nW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUGxhbkNvbmZpZ3VyYXRpb24ge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIHByaWNlOiBudW1iZXI7IC8vIGVuIGNlbnRhdm9zXG4gIGxpbWl0czogUGxhbkxpbWl0cztcbiAgZmVhdHVyZXM6IHN0cmluZ1tdO1xuICByZXN0cmljdGVkRmVhdHVyZXM6IHN0cmluZ1tdO1xufVxuXG4vLyBDb25maWd1cmFjacOzbiBjb21wbGV0YSBkZSBwbGFuZXMgY29uIGzDrW1pdGVzIHkgY2FyYWN0ZXLDrXN0aWNhc1xuZXhwb3J0IGNvbnN0IFBMQU5fQ09ORklHVVJBVElPTlM6IFJlY29yZDxzdHJpbmcsIFBsYW5Db25maWd1cmF0aW9uPiA9IHtcbiAgZnJlZToge1xuICAgIGlkOiAnZnJlZScsXG4gICAgbmFtZTogJ1BsYW4gR3JhdGlzJyxcbiAgICBwcmljZTogMCxcbiAgICBsaW1pdHM6IHtcbiAgICAgIGRvY3VtZW50czogMSxcbiAgICAgIG1pbmRNYXBzRm9yVHJpYWw6IDIsICAgICAgICAvLyBMw61taXRlIHRvdGFsIHBhcmEgZWwgdHJpYWwgZGUgNSBkw61hc1xuICAgICAgdGVzdHNGb3JUcmlhbDogMTAsICAgICAgICAgIC8vIEzDrW1pdGUgdG90YWwgcGFyYSBlbCB0cmlhbCBkZSA1IGTDrWFzXG4gICAgICBmbGFzaGNhcmRzRm9yVHJpYWw6IDEwLCAgICAgLy8gTMOtbWl0ZSB0b3RhbCBwYXJhIGVsIHRyaWFsIGRlIDUgZMOtYXNcbiAgICAgIHRva2Vuc0ZvclRyaWFsOiA1MDAwMCwgICAgICAvLyBMw61taXRlIHRvdGFsIHBhcmEgZWwgdHJpYWwgZGUgNSBkw61hc1xuICAgICAgZmVhdHVyZXM6IFsnZG9jdW1lbnRfdXBsb2FkJywgJ3Rlc3RfZ2VuZXJhdGlvbicsICdmbGFzaGNhcmRfZ2VuZXJhdGlvbicsICdtaW5kX21hcF9nZW5lcmF0aW9uJ11cbiAgICB9LFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnZG9jdW1lbnRfdXBsb2FkJyxcbiAgICAgICd0ZXN0X2dlbmVyYXRpb24nLFxuICAgICAgJ2ZsYXNoY2FyZF9nZW5lcmF0aW9uJywgXG4gICAgICAnbWluZF9tYXBfZ2VuZXJhdGlvbidcbiAgICBdLFxuICAgIHJlc3RyaWN0ZWRGZWF0dXJlczogW1xuICAgICAgJ3N0dWR5X3BsYW5uaW5nJyxcbiAgICAgICdhaV90dXRvcl9jaGF0JyxcbiAgICAgICdzdW1tYXJ5X2ExX2EyJ1xuICAgIF1cbiAgfSxcbiAgdXN1YXJpbzoge1xuICAgIGlkOiAndXN1YXJpbycsXG4gICAgbmFtZTogJ1BsYW4gVXN1YXJpbycsXG4gICAgcHJpY2U6IDEwMDAsIC8vIOKCrDEwLjAwXG4gICAgbGltaXRzOiB7XG4gICAgICBkb2N1bWVudHM6IC0xLCAvLyBpbGltaXRhZG9cbiAgICAgIG1pbmRNYXBzUGVyV2VlazogLTEsICAgICAgICAvLyBJbGltaXRhZG8gc2VtYW5hbFxuICAgICAgdGVzdHNQZXJXZWVrOiAtMSwgICAgICAgICAgIC8vIElsaW1pdGFkbyBzZW1hbmFsXG4gICAgICBmbGFzaGNhcmRzUGVyV2VlazogLTEsICAgICAgLy8gSWxpbWl0YWRvIHNlbWFuYWxcbiAgICAgIG1vbnRobHlUb2tlbnM6IDEwMDAwMDAsICAgICAvLyBMw61taXRlIG1lbnN1YWxcbiAgICAgIGZlYXR1cmVzOiBbJ2RvY3VtZW50X3VwbG9hZCcsICdhaV90dXRvcl9jaGF0JywgJ3Rlc3RfZ2VuZXJhdGlvbicsICdmbGFzaGNhcmRfZ2VuZXJhdGlvbicsICdtaW5kX21hcF9nZW5lcmF0aW9uJ11cbiAgICB9LFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnZG9jdW1lbnRfdXBsb2FkJyxcbiAgICAgICdhaV90dXRvcl9jaGF0JyxcbiAgICAgICd0ZXN0X2dlbmVyYXRpb24nLFxuICAgICAgJ2ZsYXNoY2FyZF9nZW5lcmF0aW9uJyxcbiAgICAgICdtaW5kX21hcF9nZW5lcmF0aW9uJ1xuICAgIF0sXG4gICAgcmVzdHJpY3RlZEZlYXR1cmVzOiBbXG4gICAgICAnc3R1ZHlfcGxhbm5pbmcnLFxuICAgICAgJ3N1bW1hcnlfYTFfYTInXG4gICAgXVxuICB9LFxuICBwcm86IHtcbiAgICBpZDogJ3BybycsXG4gICAgbmFtZTogJ1BsYW4gUHJvJyxcbiAgICBwcmljZTogMTUwMCwgLy8g4oKsMTUuMDBcbiAgICBsaW1pdHM6IHtcbiAgICAgIGRvY3VtZW50czogLTEsIC8vIGlsaW1pdGFkb1xuICAgICAgbWluZE1hcHNQZXJXZWVrOiAtMSwgICAgICAgIC8vIElsaW1pdGFkbyBzZW1hbmFsXG4gICAgICB0ZXN0c1BlcldlZWs6IC0xLCAgICAgICAgICAgLy8gSWxpbWl0YWRvIHNlbWFuYWxcbiAgICAgIGZsYXNoY2FyZHNQZXJXZWVrOiAtMSwgICAgICAvLyBJbGltaXRhZG8gc2VtYW5hbFxuICAgICAgbW9udGhseVRva2VuczogMTAwMDAwMCwgICAgIC8vIEzDrW1pdGUgbWVuc3VhbFxuICAgICAgZmVhdHVyZXM6IFsnZG9jdW1lbnRfdXBsb2FkJywgJ3N0dWR5X3BsYW5uaW5nJywgJ2FpX3R1dG9yX2NoYXQnLCAndGVzdF9nZW5lcmF0aW9uJywgJ2ZsYXNoY2FyZF9nZW5lcmF0aW9uJywgJ21pbmRfbWFwX2dlbmVyYXRpb24nLCAnc3VtbWFyeV9hMV9hMiddXG4gICAgfSxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ2RvY3VtZW50X3VwbG9hZCcsXG4gICAgICAnc3R1ZHlfcGxhbm5pbmcnLFxuICAgICAgJ2FpX3R1dG9yX2NoYXQnLFxuICAgICAgJ3Rlc3RfZ2VuZXJhdGlvbicsXG4gICAgICAnZmxhc2hjYXJkX2dlbmVyYXRpb24nLFxuICAgICAgJ21pbmRfbWFwX2dlbmVyYXRpb24nLFxuICAgICAgJ3N1bW1hcnlfYTFfYTInXG4gICAgXSxcbiAgICByZXN0cmljdGVkRmVhdHVyZXM6IFtdXG4gIH1cbn07XG5cbi8vIEZ1bmNpb25lcyBkZSB1dGlsaWRhZFxuZXhwb3J0IGZ1bmN0aW9uIGdldFBsYW5Db25maWd1cmF0aW9uKHBsYW5JZDogc3RyaW5nKTogUGxhbkNvbmZpZ3VyYXRpb24gfCBudWxsIHtcbiAgcmV0dXJuIFBMQU5fQ09ORklHVVJBVElPTlNbcGxhbklkXSB8fCBudWxsO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0VG9rZW5MaW1pdEZvclBsYW4ocGxhbklkOiBzdHJpbmcpOiBudW1iZXIge1xuICBjb25zdCBjb25maWcgPSBnZXRQbGFuQ29uZmlndXJhdGlvbihwbGFuSWQpO1xuICBpZiAoIWNvbmZpZykgcmV0dXJuIDUwMDAwO1xuXG4gIC8vIFBhcmEgcGxhbiBncmF0dWl0bywgdXNhciB0b2tlbnNGb3JUcmlhbFxuICBpZiAocGxhbklkID09PSAnZnJlZScpIHtcbiAgICByZXR1cm4gY29uZmlnLmxpbWl0cy50b2tlbnNGb3JUcmlhbCB8fCA1MDAwMDtcbiAgfVxuXG4gIC8vIFBhcmEgcGxhbmVzIGRlIHBhZ28sIHVzYXIgbW9udGhseVRva2Vuc1xuICByZXR1cm4gY29uZmlnLmxpbWl0cy5tb250aGx5VG9rZW5zIHx8IDEwMDAwMDA7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBoYXNGZWF0dXJlQWNjZXNzKHBsYW5JZDogc3RyaW5nLCBmZWF0dXJlOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgY29uc3QgY29uZmlnID0gZ2V0UGxhbkNvbmZpZ3VyYXRpb24ocGxhbklkKTtcbiAgaWYgKCFjb25maWcpIHJldHVybiBmYWxzZTtcblxuICAvLyBTaSBsYSBjYXJhY3RlcsOtc3RpY2EgZXN0w6EgZW4gbGEgbGlzdGEgZGUgcmVzdHJpbmdpZGFzLCBubyB0aWVuZSBhY2Nlc29cbiAgaWYgKGNvbmZpZy5yZXN0cmljdGVkRmVhdHVyZXMuaW5jbHVkZXMoZmVhdHVyZSkpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICAvLyBTaSBubyBlc3TDoSByZXN0cmluZ2lkYSwgdmVyaWZpY2FyIHNpIGVzdMOhIGVuIGxhcyBjYXJhY3RlcsOtc3RpY2FzIHBlcm1pdGlkYXNcbiAgcmV0dXJuIGNvbmZpZy5mZWF0dXJlcy5pbmNsdWRlcyhmZWF0dXJlKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFdlZWtseUxpbWl0KHBsYW5JZDogc3RyaW5nLCBsaW1pdFR5cGU6ICdtaW5kTWFwcycgfCAndGVzdHMnIHwgJ2ZsYXNoY2FyZHMnKTogbnVtYmVyIHtcbiAgY29uc3QgY29uZmlnID0gZ2V0UGxhbkNvbmZpZ3VyYXRpb24ocGxhbklkKTtcbiAgaWYgKCFjb25maWcpIHJldHVybiAwO1xuXG4gIHN3aXRjaCAobGltaXRUeXBlKSB7XG4gICAgY2FzZSAnbWluZE1hcHMnOlxuICAgICAgcmV0dXJuIGNvbmZpZy5saW1pdHMubWluZE1hcHNQZXJXZWVrIHx8IDA7XG4gICAgY2FzZSAndGVzdHMnOlxuICAgICAgcmV0dXJuIGNvbmZpZy5saW1pdHMudGVzdHNQZXJXZWVrIHx8IDA7XG4gICAgY2FzZSAnZmxhc2hjYXJkcyc6XG4gICAgICByZXR1cm4gY29uZmlnLmxpbWl0cy5mbGFzaGNhcmRzUGVyV2VlayB8fCAwO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gMDtcbiAgfVxufVxuXG4vLyBOdWV2YSBmdW5jacOzbiBwYXJhIG9idGVuZXIgbMOtbWl0ZXMgZGUgdHJpYWwgKHBhcmEgcGxhbiBncmF0dWl0bylcbmV4cG9ydCBmdW5jdGlvbiBnZXRUcmlhbExpbWl0KHBsYW5JZDogc3RyaW5nLCBsaW1pdFR5cGU6ICdtaW5kTWFwcycgfCAndGVzdHMnIHwgJ2ZsYXNoY2FyZHMnIHwgJ3Rva2VucycpOiBudW1iZXIge1xuICBjb25zdCBjb25maWcgPSBnZXRQbGFuQ29uZmlndXJhdGlvbihwbGFuSWQpO1xuICBpZiAoIWNvbmZpZyB8fCBwbGFuSWQgIT09ICdmcmVlJykgcmV0dXJuIC0xOyAvLyAtMSBwYXJhIGlsaW1pdGFkbyBvIG5vIGFwbGljYVxuXG4gIHN3aXRjaCAobGltaXRUeXBlKSB7XG4gICAgY2FzZSAnbWluZE1hcHMnOlxuICAgICAgcmV0dXJuIGNvbmZpZy5saW1pdHMubWluZE1hcHNGb3JUcmlhbCB8fCAwO1xuICAgIGNhc2UgJ3Rlc3RzJzpcbiAgICAgIHJldHVybiBjb25maWcubGltaXRzLnRlc3RzRm9yVHJpYWwgfHwgMDtcbiAgICBjYXNlICdmbGFzaGNhcmRzJzpcbiAgICAgIHJldHVybiBjb25maWcubGltaXRzLmZsYXNoY2FyZHNGb3JUcmlhbCB8fCAwO1xuICAgIGNhc2UgJ3Rva2Vucyc6XG4gICAgICByZXR1cm4gY29uZmlnLmxpbWl0cy50b2tlbnNGb3JUcmlhbCB8fCAwO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gMDtcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNVbmxpbWl0ZWQobGltaXQ6IG51bWJlcik6IGJvb2xlYW4ge1xuICByZXR1cm4gbGltaXQgPT09IC0xO1xufVxuXG4vLyBWYWxpZGFyIHNpIHVuIHVzdWFyaW8gcHVlZGUgcmVhbGl6YXIgdW5hIGFjY2nDs24gZXNwZWPDrWZpY2FcbmV4cG9ydCBmdW5jdGlvbiBjYW5QZXJmb3JtQWN0aW9uKFxuICBwbGFuSWQ6IHN0cmluZyxcbiAgZmVhdHVyZTogc3RyaW5nLFxuICBjdXJyZW50VXNhZ2U6IG51bWJlcixcbiAgbGltaXRUeXBlPzogJ21pbmRNYXBzJyB8ICd0ZXN0cycgfCAnZmxhc2hjYXJkcydcbik6IHsgYWxsb3dlZDogYm9vbGVhbjsgcmVhc29uPzogc3RyaW5nIH0ge1xuICBjb25zdCBjb25maWcgPSBnZXRQbGFuQ29uZmlndXJhdGlvbihwbGFuSWQpO1xuXG4gIGlmICghY29uZmlnKSB7XG4gICAgcmV0dXJuIHsgYWxsb3dlZDogZmFsc2UsIHJlYXNvbjogJ1BsYW4gbm8gdsOhbGlkbycgfTtcbiAgfVxuXG4gIC8vIFZlcmlmaWNhciBzaSB0aWVuZSBhY2Nlc28gYSBsYSBjYXJhY3RlcsOtc3RpY2FcbiAgaWYgKCFoYXNGZWF0dXJlQWNjZXNzKHBsYW5JZCwgZmVhdHVyZSkpIHtcbiAgICByZXR1cm4geyBhbGxvd2VkOiBmYWxzZSwgcmVhc29uOiBgQ2FyYWN0ZXLDrXN0aWNhICR7ZmVhdHVyZX0gbm8gZGlzcG9uaWJsZSBlbiAke2NvbmZpZy5uYW1lfWAgfTtcbiAgfVxuXG4gIC8vIFZlcmlmaWNhciBsw61taXRlcyBzZW1hbmFsZXMgc2kgYXBsaWNhXG4gIGlmIChsaW1pdFR5cGUpIHtcbiAgICBjb25zdCB3ZWVrbHlMaW1pdCA9IGdldFdlZWtseUxpbWl0KHBsYW5JZCwgbGltaXRUeXBlKTtcbiAgICBpZiAoIWlzVW5saW1pdGVkKHdlZWtseUxpbWl0KSAmJiBjdXJyZW50VXNhZ2UgPj0gd2Vla2x5TGltaXQpIHtcbiAgICAgIHJldHVybiB7IGFsbG93ZWQ6IGZhbHNlLCByZWFzb246IGBMw61taXRlIHNlbWFuYWwgZGUgJHtsaW1pdFR5cGV9IGFsY2FuemFkbyAoJHt3ZWVrbHlMaW1pdH0pYCB9O1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiB7IGFsbG93ZWQ6IHRydWUgfTtcbn1cblxuLy8gRnVuY2nDs24gcGFyYSB2ZXJpZmljYXIgYWNjZXNvIGRlIHVzdWFyaW8gKHBhcmEgdXNvIGVuIGZyb250ZW5kKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNoZWNrVXNlckZlYXR1cmVBY2Nlc3MoZmVhdHVyZTogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIHRyeSB7XG4gICAgLy8gT2J0ZW5lciBlbCBwbGFuIGRlbCB1c3VhcmlvIGRlc2RlIGxhIEFQSVxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvdXNlci9wbGFuJyk7XG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igb2J0ZW5pZW5kbyBwbGFuIGRlbCB1c3VhcmlvJyk7XG4gICAgICAvLyBTaSBoYXkgZXJyb3IsIGFzdW1pciBwbGFuIGdyYXR1aXRvXG4gICAgICByZXR1cm4gaGFzRmVhdHVyZUFjY2VzcygnZnJlZScsIGZlYXR1cmUpO1xuICAgIH1cblxuICAgIGNvbnN0IHsgcGxhbiB9ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIGNvbnN0IHVzZXJQbGFuID0gcGxhbiB8fCAnZnJlZSc7XG5cbiAgICAvLyBVc2FyIGxhIG1pc21hIGzDs2dpY2EgcXVlIGxhIGZ1bmNpw7NuIGhhc0ZlYXR1cmVBY2Nlc3NcbiAgICByZXR1cm4gaGFzRmVhdHVyZUFjY2Vzcyh1c2VyUGxhbiwgZmVhdHVyZSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdmVyaWZpY2FuZG8gYWNjZXNvIGEgY2FyYWN0ZXLDrXN0aWNhOicsIGVycm9yKTtcbiAgICAvLyBFbiBjYXNvIGRlIGVycm9yLCBhc3VtaXIgcGxhbiBncmF0dWl0b1xuICAgIHJldHVybiBoYXNGZWF0dXJlQWNjZXNzKCdmcmVlJywgZmVhdHVyZSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJQTEFOX0NPTkZJR1VSQVRJT05TIiwiZnJlZSIsImlkIiwibmFtZSIsInByaWNlIiwibGltaXRzIiwiZG9jdW1lbnRzIiwibWluZE1hcHNGb3JUcmlhbCIsInRlc3RzRm9yVHJpYWwiLCJmbGFzaGNhcmRzRm9yVHJpYWwiLCJ0b2tlbnNGb3JUcmlhbCIsImZlYXR1cmVzIiwicmVzdHJpY3RlZEZlYXR1cmVzIiwidXN1YXJpbyIsIm1pbmRNYXBzUGVyV2VlayIsInRlc3RzUGVyV2VlayIsImZsYXNoY2FyZHNQZXJXZWVrIiwibW9udGhseVRva2VucyIsInBybyIsImdldFBsYW5Db25maWd1cmF0aW9uIiwicGxhbklkIiwiZ2V0VG9rZW5MaW1pdEZvclBsYW4iLCJjb25maWciLCJoYXNGZWF0dXJlQWNjZXNzIiwiZmVhdHVyZSIsImluY2x1ZGVzIiwiZ2V0V2Vla2x5TGltaXQiLCJsaW1pdFR5cGUiLCJnZXRUcmlhbExpbWl0IiwiaXNVbmxpbWl0ZWQiLCJsaW1pdCIsImNhblBlcmZvcm1BY3Rpb24iLCJjdXJyZW50VXNhZ2UiLCJhbGxvd2VkIiwicmVhc29uIiwid2Vla2x5TGltaXQiLCJjaGVja1VzZXJGZWF0dXJlQWNjZXNzIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiY29uc29sZSIsImVycm9yIiwicGxhbiIsImpzb24iLCJ1c2VyUGxhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/planLimits.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();