"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_supabase_tokenUsageService_server_ts";
exports.ids = ["_rsc_src_lib_supabase_tokenUsageService_server_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/supabase/tokenUsageService.server.ts":
/*!******************************************************!*\
  !*** ./src/lib/supabase/tokenUsageService.server.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTokenUsageStats: () => (/* binding */ getTokenUsageStats),\n/* harmony export */   saveTokenUsageServer: () => (/* binding */ saveTokenUsageServer),\n/* harmony export */   updateUserPlanLimits: () => (/* binding */ updateUserPlanLimits)\n/* harmony export */ });\n/* harmony import */ var _server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\n\n\n/**\n * Guarda el uso de tokens en Supabase (versión servidor)\n */ async function saveTokenUsageServer(data) {\n    try {\n        console.log('🔄 saveTokenUsageServer iniciado con data:', data);\n        // Crear cliente de Supabase para el servidor\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        console.log('✅ Cliente Supabase del servidor creado');\n        // Obtener el usuario actual\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');\n        if (userError || !user) {\n            console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);\n            return;\n        }\n        const usageRecord = {\n            user_id: user.id,\n            activity_type: data.activity,\n            model_name: data.model,\n            prompt_tokens: data.usage.promptTokens,\n            completion_tokens: data.usage.completionTokens,\n            total_tokens: data.usage.totalTokens,\n            estimated_cost: data.usage.estimatedCost || 0,\n            usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01\n        };\n        console.log('📝 Registro a insertar:', usageRecord);\n        const { error } = await supabase.from('user_token_usage').insert([\n            usageRecord\n        ]);\n        if (error) {\n            console.error('❌ Error al guardar uso de tokens:', error);\n            return;\n        }\n        console.log('✅ Registro insertado exitosamente en user_token_usage');\n        // Validar límites antes de actualizar contador\n        const canUpdate = await validateTokenLimits(supabase, user.id, data.usage.totalTokens);\n        if (!canUpdate.allowed) {\n            console.warn('⚠️ Límite de tokens alcanzado:', canUpdate.reason);\n            // Aún así guardamos el registro para auditoría, pero marcamos el exceso\n            return;\n        }\n        // Actualizar contador mensual del usuario\n        await updateMonthlyTokenCount(supabase, user.id, data.usage.totalTokens);\n    } catch (error) {\n        console.error('❌ Error en saveTokenUsageServer:', error);\n    }\n}\n/**\n * Actualiza el contador mensual de tokens del usuario\n */ async function updateMonthlyTokenCount(supabase, userId, tokens) {\n    try {\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Obtener o crear perfil del usuario\n        let { data: profile, error: profileError } = await supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (profileError && profileError.code !== 'PGRST116') {\n            console.error('Error al obtener perfil:', profileError);\n            return;\n        }\n        if (!profile) {\n            // Crear perfil nuevo con límites dinámicos\n            const defaultPlan = 'free';\n            const tokenLimit = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(defaultPlan);\n            const { error: insertError } = await supabase.from('user_profiles').insert([\n                {\n                    user_id: userId,\n                    subscription_plan: defaultPlan,\n                    monthly_token_limit: tokenLimit,\n                    current_month_tokens: tokens,\n                    current_month: currentMonth,\n                    payment_verified: false\n                }\n            ]);\n            if (insertError) {\n                console.error('Error al crear perfil:', insertError);\n            } else {\n                console.log('✅ Perfil de usuario creado con límite dinámico:', tokenLimit);\n            }\n        } else {\n            // Actualizar perfil existente\n            const newTokenCount = profile.current_month === currentMonth ? profile.current_month_tokens + tokens : tokens; // Reset si es nuevo mes\n            const { error: updateError } = await supabase.from('user_profiles').update({\n                current_month_tokens: newTokenCount,\n                current_month: currentMonth,\n                updated_at: new Date().toISOString()\n            }).eq('user_id', userId);\n            if (updateError) {\n                console.error('Error al actualizar perfil:', updateError);\n            } else {\n                console.log('✅ Perfil de usuario actualizado');\n            }\n        }\n    } catch (error) {\n        console.error('Error en updateMonthlyTokenCount:', error);\n    }\n}\n/**\n * Valida si el usuario puede usar la cantidad de tokens especificada\n */ async function validateTokenLimits(supabase, userId, tokensToUse) {\n    try {\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Obtener perfil del usuario\n        const { data: profile, error } = await supabase.from('user_profiles').select('subscription_plan, monthly_token_limit, current_month_tokens, current_month, payment_verified').eq('user_id', userId).single();\n        if (error || !profile) {\n            return {\n                allowed: false,\n                reason: 'Perfil de usuario no encontrado'\n            };\n        }\n        // Verificar pago para planes de pago\n        if (profile.subscription_plan !== 'free' && !profile.payment_verified) {\n            return {\n                allowed: false,\n                reason: 'Pago no verificado'\n            };\n        }\n        // Calcular tokens actuales (reset si es nuevo mes)\n        let currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n        // Verificar límite\n        if (currentTokens + tokensToUse > profile.monthly_token_limit) {\n            return {\n                allowed: false,\n                reason: `Límite mensual alcanzado: ${currentTokens + tokensToUse}/${profile.monthly_token_limit}`,\n                currentUsage: {\n                    current: currentTokens,\n                    limit: profile.monthly_token_limit,\n                    requested: tokensToUse,\n                    plan: profile.subscription_plan\n                }\n            };\n        }\n        return {\n            allowed: true,\n            currentUsage: {\n                current: currentTokens,\n                limit: profile.monthly_token_limit,\n                remaining: profile.monthly_token_limit - currentTokens - tokensToUse\n            }\n        };\n    } catch (error) {\n        console.error('Error validating token limits:', error);\n        return {\n            allowed: false,\n            reason: 'Error de validación'\n        };\n    }\n}\n/**\n * Obtiene estadísticas de uso de tokens del usuario\n */ async function getTokenUsageStats(userId) {\n    try {\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        const { data: profile, error } = await supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error || !profile) {\n            return null;\n        }\n        // Reset si es nuevo mes\n        let currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n        const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(profile.subscription_plan);\n        const percentage = currentTokens / profile.monthly_token_limit * 100;\n        return {\n            currentMonth: {\n                used: currentTokens,\n                limit: profile.monthly_token_limit,\n                percentage: Math.round(percentage),\n                remaining: profile.monthly_token_limit - currentTokens\n            },\n            plan: {\n                name: planConfig?.name || profile.subscription_plan,\n                features: planConfig?.features || []\n            },\n            paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'\n        };\n    } catch (error) {\n        console.error('Error getting token usage stats:', error);\n        return null;\n    }\n}\n/**\n * Actualiza los límites de tokens cuando cambia el plan del usuario\n */ async function updateUserPlanLimits(userId, newPlan) {\n    try {\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const newTokenLimit = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(newPlan);\n        const { error } = await supabase.from('user_profiles').update({\n            subscription_plan: newPlan,\n            monthly_token_limit: newTokenLimit,\n            updated_at: new Date().toISOString()\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error updating user plan limits:', error);\n            return false;\n        }\n        console.log(`✅ Plan actualizado para usuario ${userId}: ${newPlan} (${newTokenLimit} tokens)`);\n        return true;\n    } catch (error) {\n        console.error('Error updating user plan limits:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/tokenUsageService.server.ts\n");

/***/ })

};
;