'use client';

import React, { useState } from 'react';
import { FiShoppingCart, FiX, FiCreditCard } from 'react-icons/fi';
import toast from 'react-hot-toast';

interface TokenPurchaseButtonProps {
  userPlan: 'free' | 'usuario' | 'pro';
  currentTokens: number;
  tokenLimit: number;
}

export default function TokenPurchaseButton({ 
  userPlan, 
  currentTokens, 
  tokenLimit 
}: TokenPurchaseButtonProps) {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);

  // No mostrar para usuarios gratuitos
  if (userPlan === 'free') {
    return null;
  }

  const handlePurchase = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/tokens/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAmount: 1000000, // 1 millón de tokens
          price: 10.00 // 10€
        }),
      });

      const data = await response.json();

      if (response.ok && data.url) {
        // Redirigir a Stripe Checkout
        window.location.href = data.url;
      } else {
        toast.error(data.error || 'Error al procesar la compra');
      }
    } catch (error) {
      console.error('Error en compra de tokens:', error);
      toast.error('Error al procesar la compra');
    } finally {
      setLoading(false);
    }
  };

  const formatTokens = (tokens: number | null | undefined) => {
    const validTokens = tokens || 0;
    if (validTokens >= 1000000) {
      return `${(validTokens / 1000000).toFixed(1)}M`;
    }
    if (validTokens >= 1000) {
      return `${(validTokens / 1000).toFixed(1)}K`;
    }
    return validTokens.toLocaleString();
  };

  const usagePercentage = tokenLimit > 0 ? (currentTokens / tokenLimit) * 100 : 0;

  return (
    <>
      {/* Botón principal */}
      <button
        onClick={() => setShowModal(true)}
        className={`w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
          usagePercentage >= 80
            ? 'bg-blue-600 hover:bg-blue-700 text-white'
            : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
        }`}
      >
        <FiShoppingCart className="w-4 h-4" />
        <span>Comprar más tokens</span>
      </button>

      {/* Modal de confirmación */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <FiCreditCard className="w-6 h-6 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">
                  Comprar Tokens Adicionales
                </h2>
              </div>
              <button
                onClick={() => setShowModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <FiX className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-4">
              {/* Información del paquete */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 mb-2">
                  Paquete de Tokens Adicionales
                </h3>
                <div className="space-y-2 text-sm text-blue-800">
                  <div className="flex justify-between">
                    <span>Tokens incluidos:</span>
                    <span className="font-semibold">1,000,000 tokens</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Precio:</span>
                    <span className="font-semibold">10,00€ (IVA incluido)</span>
                  </div>
                </div>
              </div>

              {/* Estado actual */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Tu estado actual</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Tokens usados:</span>
                    <span>{formatTokens(currentTokens)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Límite actual:</span>
                    <span>{formatTokens(tokenLimit)}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Nuevo límite:</span>
                    <span className="text-green-600">
                      {formatTokens(tokenLimit + 1000000)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Información importante */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-xs text-yellow-800">
                  <strong>Nota:</strong> Los tokens adicionales se añadirán a tu límite mensual 
                  actual y estarán disponibles inmediatamente después del pago.
                </p>
              </div>
            </div>

            {/* Footer */}
            <div className="flex space-x-3 p-6 border-t border-gray-200">
              <button
                onClick={() => setShowModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handlePurchase}
                disabled={loading}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Procesando...</span>
                  </>
                ) : (
                  <>
                    <FiCreditCard className="w-4 h-4" />
                    <span>Comprar por 10,00€</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
